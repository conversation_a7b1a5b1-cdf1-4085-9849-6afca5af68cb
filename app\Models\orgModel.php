<?php

namespace App\Models;

use CodeIgniter\Model;

class orgModel extends Model
{
    protected $table      = 'dakoii_org';
    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;

    protected $returnType     = 'array';
    protected $useSoftDeletes = false;

    protected $allowedFields = [
        'orgcode',
        'name',
        'org_name',  // Alternative column name
        'organization_name',  // Another alternative
        'title',  // Another possible alternative
        'description',
        'addlockprov',
        'addlockcountry',
        'orglogo',
        'is_locationlocked',
        'province_json',
        'district_json',
        'llg_json',
        'postal_address',
        'phones',
        'emails',
        'is_active',
        'license_status',
        'signature_filepath',
        'signature_position',
        'signature_name',
        'stamp_filepath',
        'approved_stamp_filepath',
        'created_by',
        'updated_by'
    ];

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = '';

    protected $validationRules = [

    ];

    protected $validationMessages = [

    ];

    protected $skipValidation = false;

    // Database field types for reference
    protected $fieldTypes = [
        'id' => 'int',
        'orgcode' => 'varchar',
        'name' => 'varchar',
        'org_name' => 'varchar',
        'organization_name' => 'varchar',
        'title' => 'varchar',
        'description' => 'text',
        'addlockprov' => 'varchar',
        'addlockcountry' => 'varchar',
        'orglogo' => 'varchar',
        'is_locationlocked' => 'tinyint',
        'province_json' => 'varchar',
        'district_json' => 'varchar',
        'llg_json' => 'varchar',
        'postal_address' => 'text',
        'phones' => 'text',
        'emails' => 'text',
        'is_active' => 'tinyint',
        'license_status' => 'varchar',
        'signature_filepath' => 'varchar',
        'signature_position' => 'varchar',
        'signature_name' => 'varchar',
        'stamp_filepath' => 'varchar',
        'approved_stamp_filepath' => 'varchar',
        'created_by' => 'int',
        'updated_by' => 'int',
        'created_at' => 'timestamp',
        'updated_at' => 'timestamp'
    ];

    /**
     * Get organization name with fallback to alternative column names
     *
     * @param array $org Organization data array
     * @return string Organization name
     */
    public function getOrganizationName($org)
    {
        if (empty($org)) {
            return 'Unknown Organization';
        }

        // Try different possible column names for organization name
        $nameFields = ['name', 'org_name', 'organization_name', 'title'];

        foreach ($nameFields as $field) {
            if (isset($org[$field]) && !empty($org[$field])) {
                return $org[$field];
            }
        }

        // Fallback to orgcode if no name field is found
        return $org['orgcode'] ?? 'Unknown Organization';
    }

    /**
     * Override find method to add name handling
     */
    public function find($id = null)
    {
        $result = parent::find($id);

        if ($result && is_array($result)) {
            // Ensure 'name' field exists for backward compatibility
            if (!isset($result['name'])) {
                $result['name'] = $this->getOrganizationName($result);
            }
        }

        return $result;
    }

    /**
     * Override findAll method to add name handling
     */
    public function findAll(int $limit = 0, int $offset = 0)
    {
        $results = parent::findAll($limit, $offset);

        if ($results && is_array($results)) {
            foreach ($results as &$result) {
                // Ensure 'name' field exists for backward compatibility
                if (!isset($result['name'])) {
                    $result['name'] = $this->getOrganizationName($result);
                }
            }
        }

        return $results;
    }
}
